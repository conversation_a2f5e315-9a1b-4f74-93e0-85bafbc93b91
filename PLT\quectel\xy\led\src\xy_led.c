#include <stdint.h>
#include <stdbool.h>
#include "osa.h"
#include "ql_wifi.h"
#include "xy_led_priv.h"
#include "xy_charger.h"
#include "xy_log.h"
#include "uapapi.h"
#include "ql_sim.h"
#include "mm_api.h"
#include "battery_soc.h"
#include "wan_applet.h"

static const char led_tag[] = "LED";
static OSMsgQRef s_evt_q = NULL;
static uint8_t s_led_stack[LED_TASK_STACK_SIZE];
static OSATaskRef s_led_task = NULL;
static uint8_t s_status_stack[STATUS_TASK_STACK_SIZE];
static OSATaskRef s_status_task = NULL;

/* LED 配置表 - 顺序对应 led_idx_e 枚举 */
static led_config_t s_led_cfgs[LED_COUNT] = {
    [LED_IDX_NET_4G_RED] = {.gpio_pin = GPIO_28, .pin_num = 28, .name = "4G_Red", .status = LED_OFF},
    [LED_IDX_NET_4G_GREEN] = {.gpio_pin = GPIO_20, .pin_num = 27, .name = "4G_Green", .status = LED_OFF},
    [LED_IDX_NET_5G_RED] = {.gpio_pin = GPIO_26, .pin_num = 30, .name = "5G_Red", .status = LED_OFF},
    [LED_IDX_NET_5G_GREEN] = {.gpio_pin = GPIO_27, .pin_num = 29, .name = "5G_Green", .status = LED_OFF},
    [LED_IDX_WIFI_GREEN] = {.gpio_pin = GPIO_19, .pin_num = 91, .name = "WiFi_Green", .status = LED_OFF},
    [LED_IDX_CHARGER_RED] = {.gpio_pin = GPIO_25, .pin_num = 31, .name = "Charger_Red", .status = LED_OFF},
    [LED_IDX_CHARGER_GREEN] = {.gpio_pin = GPIO_8, .pin_num = 127, .name = "Charger_Green", .status = LED_OFF},
};

static volatile bool s_exit_flag;                 /* 共享退出标志 */
static volatile led_mode_e s_led_mode;            /* LED工作模式 */
static volatile bool s_charging_state;            /* 当前充电状态 */
static volatile uint32_t s_led_active_until_tick; /* LED活跃截止时间 */

static led_net_status_e check_network_status(void)
{
    if (ql_sim_get_card_status() != 1)
    {
        return NET_STATUS_NO_SIM;
    }

    if (cmGetNwStatus() != 1)
    {
        return NET_STATUS_NO_REG;
    }

    UINT32 handle = (ql_sim_get_cur_SimId() == 0) ? IND_REQ_HANDLE : IND_REQ_HANDLE_1;

    /* 使用静态变量减少栈使用 */
    static struct mmExtendedSignal signal;
    memset(&signal, 0, sizeof(signal));
    getExtendedSignal(handle, &signal);

    static api_siminfo_st sim_info;
    memset(&sim_info, 0, sizeof(sim_info));
    if (ql_api_siminfo(&sim_info) != 0)
    {
        XY_LOGE(led_tag, "Failed to get SIM info, defaulting to 4G");
        sim_info.nettype = 4;
    }

    bool good_signal;
    if (sim_info.nettype == 5)
    {
        good_signal = (signal.ssRSRP != 255 && signal.ssRSRP >= 30);
        return good_signal ? NET_STATUS_5G_REG_GOOD : NET_STATUS_5G_REG_POOR;
    }
    else
    {
        good_signal = (signal.rsrp != 255 && signal.rsrp >= 30);
        return good_signal ? NET_STATUS_4G_REG_GOOD : NET_STATUS_4G_REG_POOR;
    }
}

static led_wifi_status_e check_wifi_status(void)
{
    unsigned char uap_status = UAP_STATUS();
    if (uap_status != UAP_BSS_START)
    {
        return WIFI_STATUS_OFF;
    }

    int sta_num = GetWlanClientNum();
    return (sta_num >= 1) ? WIFI_STATUS_CONNECTED : WIFI_STATUS_ON;
}

static led_charger_status_e check_charger_status(void)
{
    // int bat_level = xy_charger_get_battery_level();
    // if (bat_level < 0)
    // {
    //     XY_LOGE(led_tag, "Failed to get battery level");
    //     return CHARGER_STATUS_NORMAL_BATTERY;
    // }

    xy_charge_status_t charge_status = get_battery_charge_status();
    bool is_charging = (charge_status == XY_CHARGE_STATUS_PRECHG ||
                        charge_status == XY_CHARGE_STATUS_FASTCHG);
    s_charging_state = is_charging;

    // if (bat_level < LOW_BATTERY_THRESHOLD)
    // {
    //     return is_charging ? CHARGER_STATUS_LOW_BATTERY_CHARGING
    //                        : CHARGER_STATUS_LOW_BATTERY;
    // }
    return is_charging ? CHARGER_STATUS_NORMAL_BATTERY_CHARGING
                       : CHARGER_STATUS_NORMAL_BATTERY;
}

/* GPIO初始化 */
static int xy_led_gpio_init(void)
{
    int i;

    for (i = 0; i < LED_COUNT; ++i)
    {
        int ret;

        ret = ql_pin_set_func(s_led_cfgs[i].pin_num, 0);
        if (ret != QL_GPIO_SUCCESS)
        {
            XY_LOGE(led_tag,
                    "Set pin gpio func fail for %s, PIN%d, ret=%d",
                    s_led_cfgs[i].name, s_led_cfgs[i].pin_num, ret);
            return -1;
        }

        ret = ql_gpio_init(s_led_cfgs[i].gpio_pin, GPIO_OUTPUT, PULL_NONE, GPIO_LOW);
        if (ret != QL_GPIO_SUCCESS)
        {
            XY_LOGE(led_tag,
                    "Init %s fail, GPIO%d, ret=%d",
                    s_led_cfgs[i].name, s_led_cfgs[i].gpio_pin, ret);
            return -1;
        }

        XY_LOGI(led_tag, "%s on GPIO%d (PIN%d)", s_led_cfgs[i].name, s_led_cfgs[i].gpio_pin, s_led_cfgs[i].pin_num);
    }

    return 0;
}

static void apply_net_state(led_net_status_e state)
{
    if (s_led_mode != LED_MODE_NORMAL)
    {
        return;
    }

    switch (state)
    {
    case NET_STATUS_NO_SIM: /* 不存在SIM卡或PIN码未解锁：4G和5G红灯都常亮 */
        s_led_cfgs[LED_IDX_NET_4G_RED].status = LED_ON;
        s_led_cfgs[LED_IDX_NET_4G_GREEN].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_5G_RED].status = LED_ON;
        s_led_cfgs[LED_IDX_NET_5G_GREEN].status = LED_OFF;
        break;

    case NET_STATUS_NO_REG: /* 存在SIM卡但未注册：4G和5G红灯都闪烁 */
        s_led_cfgs[LED_IDX_NET_4G_RED].status = LED_BLINK_1HZ;
        s_led_cfgs[LED_IDX_NET_4G_GREEN].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_5G_RED].status = LED_BLINK_1HZ;
        s_led_cfgs[LED_IDX_NET_5G_GREEN].status = LED_OFF;
        break;

    case NET_STATUS_4G_REG_GOOD: /* 4G注册且信号好：4G绿灯常亮，5G全灭 */
        s_led_cfgs[LED_IDX_NET_4G_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_4G_GREEN].status = LED_ON;
        s_led_cfgs[LED_IDX_NET_5G_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_5G_GREEN].status = LED_OFF;
        break;

    case NET_STATUS_4G_REG_POOR: /* 4G注册但信号差：4G绿灯闪烁，5G全灭 */
        s_led_cfgs[LED_IDX_NET_4G_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_4G_GREEN].status = LED_BLINK_1HZ;
        s_led_cfgs[LED_IDX_NET_5G_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_5G_GREEN].status = LED_OFF;
        break;

    case NET_STATUS_5G_REG_GOOD: /* 5G注册且信号好：5G绿灯常亮，4G全灭 */
        s_led_cfgs[LED_IDX_NET_4G_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_4G_GREEN].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_5G_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_5G_GREEN].status = LED_ON;
        break;

    case NET_STATUS_5G_REG_POOR: /* 5G注册但信号差：5G绿灯闪烁，4G全灭 */
        s_led_cfgs[LED_IDX_NET_4G_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_4G_GREEN].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_5G_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_5G_GREEN].status = LED_BLINK_1HZ;
        break;

    default:
        break;
    }
}

static void apply_wifi_state(led_wifi_status_e state)
{
    if (s_led_mode != LED_MODE_NORMAL)
    {
        return;
    }

    switch (state)
    {
    case WIFI_STATUS_OFF: /* WiFi关闭：绿灯灭 */
        s_led_cfgs[LED_IDX_WIFI_GREEN].status = LED_OFF;
        break;

    case WIFI_STATUS_ON: /* WiFi开启但无设备连接：绿灯常亮 */
        s_led_cfgs[LED_IDX_WIFI_GREEN].status = LED_ON;
        break;

    case WIFI_STATUS_CONNECTED: /* WiFi开启且有设备连接：绿灯500ms亮，500ms灭 */
        s_led_cfgs[LED_IDX_WIFI_GREEN].status = LED_BLINK_1HZ;
        break;

    default:
        break;
    }
}

static void apply_charger_state(led_charger_status_e state)
{
    if (s_led_mode != LED_MODE_NORMAL)
    {
        return;
    }

    /* 检查是否处于休眠状态（超过活跃时间且非充电状态） */
    bool is_in_sleep = (OSAGetTicks() > s_led_active_until_tick);

    switch (state)
    {
    case CHARGER_STATUS_LOW_BATTERY: /* 电量<20%：红灯亮，绿灯灭 */
        if (is_in_sleep)
        {
            /* 休眠状态下非充电时熄灭充电指示灯 */
            s_led_cfgs[LED_IDX_CHARGER_RED].status = LED_OFF;
            s_led_cfgs[LED_IDX_CHARGER_GREEN].status = LED_OFF;
        }
        else
        {
            s_led_cfgs[LED_IDX_CHARGER_RED].status = LED_ON;
            s_led_cfgs[LED_IDX_CHARGER_GREEN].status = LED_OFF;
        }
        break;

    case CHARGER_STATUS_LOW_BATTERY_CHARGING: /* 电量<20%且充电中：红灯闪烁，绿灯灭 */
        s_led_cfgs[LED_IDX_CHARGER_RED].status = LED_BLINK_1HZ;
        s_led_cfgs[LED_IDX_CHARGER_GREEN].status = LED_OFF;
        break;

    case CHARGER_STATUS_NORMAL_BATTERY: /* 电量≥20%或充满电：红灯灭，绿灯亮 */
        if (is_in_sleep)
        {
            /* 休眠状态下非充电时熄灭充电指示灯 */
            s_led_cfgs[LED_IDX_CHARGER_RED].status = LED_OFF;
            s_led_cfgs[LED_IDX_CHARGER_GREEN].status = LED_OFF;
        }
        else
        {
            s_led_cfgs[LED_IDX_CHARGER_RED].status = LED_OFF;
            s_led_cfgs[LED_IDX_CHARGER_GREEN].status = LED_ON;
        }
        break;

    case CHARGER_STATUS_NORMAL_BATTERY_CHARGING: /* 电量≥20%且充电中：红灯灭，绿灯闪烁 */
        s_led_cfgs[LED_IDX_CHARGER_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_CHARGER_GREEN].status = LED_BLINK_1HZ;
        break;

    default:
        break;
    }
}

static void apply_test_mode(led_mode_e mode)
{
    s_led_mode = mode;

    if (mode == LED_MODE_TEST)
    {
        s_led_cfgs[LED_IDX_NET_4G_RED].status = LED_TEST_RED_GREEN;
        s_led_cfgs[LED_IDX_NET_4G_GREEN].status = LED_TEST_RED_GREEN;
        s_led_cfgs[LED_IDX_NET_5G_RED].status = LED_TEST_RED_GREEN;
        s_led_cfgs[LED_IDX_NET_5G_GREEN].status = LED_TEST_RED_GREEN;
        s_led_cfgs[LED_IDX_CHARGER_RED].status = LED_TEST_RED_GREEN;
        s_led_cfgs[LED_IDX_CHARGER_GREEN].status = LED_TEST_RED_GREEN;

        s_led_cfgs[LED_IDX_WIFI_GREEN].status = LED_TEST_WIFI_ON;
    }
    else
    {
        int i;
        for (i = 0; i < LED_COUNT; i++)
        {
            s_led_cfgs[i].status = LED_OFF;
        }

        uint32_t new_active_until = OSAGetTicks() + LED_KEY_ACTIVE_TIME_TICK;
        if (new_active_until > s_led_active_until_tick)
        {
            s_led_active_until_tick = new_active_until;
        }
    }
}

static void apply_sleep_state(void)
{
    if (s_led_mode != LED_MODE_NORMAL)
    {
        return;
    }

    s_led_cfgs[LED_IDX_NET_4G_RED].status = LED_OFF;
    s_led_cfgs[LED_IDX_NET_4G_GREEN].status = LED_OFF;
    s_led_cfgs[LED_IDX_NET_5G_RED].status = LED_OFF;
    s_led_cfgs[LED_IDX_NET_5G_GREEN].status = LED_OFF;
    s_led_cfgs[LED_IDX_WIFI_GREEN].status = LED_OFF;

    /* 休眠状态下，如果不是充电状态则熄灭充电指示灯 */
    if (!s_charging_state)
    {
        s_led_cfgs[LED_IDX_CHARGER_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_CHARGER_GREEN].status = LED_OFF;
    }
}

static void led_apply_level(unsigned idx, int blink_on)
{
    static int last_gpio_state[LED_COUNT] = {
        GPIO_LOW, GPIO_LOW, GPIO_LOW,
        GPIO_LOW, GPIO_LOW, GPIO_LOW,
        GPIO_LOW};

    int new_state;

    switch (s_led_cfgs[idx].status)
    {
    case LED_OFF:
        new_state = GPIO_LOW;
        break;

    case LED_ON:
        new_state = GPIO_HIGH;
        break;

    case LED_BLINK_1HZ:
        new_state = blink_on ? GPIO_HIGH : GPIO_LOW;
        break;

    case LED_TEST_RED_GREEN:
        if (idx == LED_IDX_NET_4G_RED || idx == LED_IDX_NET_5G_RED || idx == LED_IDX_CHARGER_RED)
        {
            new_state = blink_on ? GPIO_HIGH : GPIO_LOW;
        }
        else if (idx == LED_IDX_NET_4G_GREEN || idx == LED_IDX_NET_5G_GREEN || idx == LED_IDX_CHARGER_GREEN)
        {
            new_state = blink_on ? GPIO_LOW : GPIO_HIGH;
        }
        else
        {
            new_state = GPIO_LOW;
        }
        break;

    case LED_TEST_WIFI_ON:
        new_state = GPIO_HIGH;
        break;
    }

    if (new_state != last_gpio_state[idx])
    {
        ql_gpio_set_level(s_led_cfgs[idx].gpio_pin, new_state);
        last_gpio_state[idx] = new_state;
    }
}

/* LED 任务 */
static void led_task_entry(void *arg)
{
    XY_LOGI(led_tag, "LED control task start");

    int blink_on = 0;
    int i;
    uint32_t last_toggle_tick = OSAGetTicks();

    while (!s_exit_flag)
    {
        uint32_t current_tick = OSAGetTicks();
        uint32_t blink_period = (s_led_mode == LED_MODE_TEST) ? BLINK_TEST_PERIOD_TICK : BLINK_1HZ_PERIOD_TICK;
        uint32_t elapsed = current_tick - last_toggle_tick;
        uint32_t to_next = (elapsed >= blink_period) ? 0 : (blink_period - elapsed);

        led_event_msg_t msg;
        int rc = OSAMsgQRecv(s_evt_q, (UINT8 *)&msg, sizeof(msg), to_next);

        if (rc == OS_SUCCESS)
        {
            do
            {
                if (s_led_mode != LED_MODE_NORMAL)
                {
                    if (msg.event_type == LED_EVENT_TEST_MODE_CHANGED)
                    {
                        apply_test_mode(msg.data.test_mode);
                    }
                }
                else
                {
                    switch (msg.event_type)
                    {
                    case LED_EVENT_NET_STATUS_CHANGED:
                        apply_net_state(msg.data.net_status);
                        break;

                    case LED_EVENT_WIFI_STATUS_CHANGED:
                        apply_wifi_state(msg.data.wifi_status);
                        break;

                    case LED_EVENT_CHARGER_STATUS_CHANGED:
                        apply_charger_state(msg.data.charger_status);
                        break;

                    case LED_EVENT_TEST_MODE_CHANGED:
                        apply_test_mode(msg.data.test_mode);
                        break;

                    case LED_EVENT_KEY_PRESSED:
                    {
                        uint32_t new_active_until = OSAGetTicks() + LED_KEY_ACTIVE_TIME_TICK;
                        if (new_active_until > s_led_active_until_tick)
                        {
                            s_led_active_until_tick = new_active_until;
                        }
                    }
                    break;

                    case LED_EVENT_SLEEP_CHANGED:
                        apply_sleep_state();
                        break;

                    default:
                        XY_LOGE(led_tag, "Unknown event type: %d", msg.event_type);
                        break;
                    }
                }
            } while (OSAMsgQRecv(s_evt_q, (UINT8 *)&msg, sizeof(msg), OS_NO_SUSPEND) == OS_SUCCESS);
        }
        else
        {
            /* 闪烁周期到达 */
            blink_on = !blink_on;
            last_toggle_tick = OSAGetTicks();
        }

        for (i = 0; i < LED_COUNT; i++)
        {
            led_apply_level(i, blink_on);
        }
    }

    /* 退出前关闭所有LED */
    XY_LOGI(led_tag, "LED task exit, turn off all LEDs");
    for (i = 0; i < LED_COUNT; i++)
    {
        s_led_cfgs[i].status = LED_OFF;
        led_apply_level(i, 0);
    }

    XY_LOGI(led_tag, "LED control task exit");
    s_led_task = NULL;
    OSATaskDelete(NULL);
}

/* 状态轮询任务 */
static void led_status_task_entry(void *arg)
{
    XY_LOGI(led_tag, "Status task start");

    led_net_status_e last_net = (led_net_status_e)-1;
    led_wifi_status_e last_wifi = (led_wifi_status_e)-1;
    led_charger_status_e last_charger = (led_charger_status_e)-1;

    led_net_status_e net;
    led_wifi_status_e wifi;
    led_charger_status_e charger;

    static led_event_msg_t msg;
    bool wakeup_charger = false;
    bool wake_flag = false;

    while (!s_exit_flag) /* 检查退出标志 */
    {
        OSATaskSleep(TASK_SLEEP_POLL_TICK);

        if (s_led_mode != LED_MODE_NORMAL)
        {
            last_net = (led_net_status_e)-1;
            last_wifi = (led_wifi_status_e)-1;
            last_charger = (led_charger_status_e)-1;
            continue;
        }

        charger = check_charger_status();

        /* 网络和WiFi指示灯的休眠逻辑：超时或者充电时就休眠 */
        if (OSAGetTicks() > s_led_active_until_tick || s_charging_state)
        {
            if (last_net != (led_net_status_e)-1 || last_wifi != (led_wifi_status_e)-1)
            {
                last_net = (led_net_status_e)-1;
                last_wifi = (led_wifi_status_e)-1;
                wake_flag = true;
                msg.event_type = LED_EVENT_SLEEP_CHANGED;
                if (OSAMsgQSend(s_evt_q, sizeof(led_event_msg_t), (UINT8 *)&msg,
                                OS_NO_SUSPEND) != OS_SUCCESS)
                {
                    XY_LOGE(led_tag, "Failed to send sleep state event");
                }
            }
        }
        else
        {
            if (wake_flag == true)
            {
                wake_flag = false;
                wakeup_charger = true;
            }

            net = check_network_status();
            wifi = check_wifi_status();

            if (net != last_net)
            {
                XY_LOGI(led_tag, "Sending network status event: %d -> %d", last_net, net);
                msg.event_type = LED_EVENT_NET_STATUS_CHANGED;
                msg.data.net_status = net;

                if (OSAMsgQSend(s_evt_q, sizeof(led_event_msg_t), (UINT8 *)&msg,
                                OS_NO_SUSPEND) != OS_SUCCESS)
                {
                    XY_LOGE(led_tag, "Failed to send network status event");
                }

                last_net = net;
            }

            if (wifi != last_wifi)
            {
                XY_LOGI(led_tag, "Sending WiFi status event: %d -> %d", last_wifi, wifi);
                msg.event_type = LED_EVENT_WIFI_STATUS_CHANGED;
                msg.data.wifi_status = wifi;

                if (OSAMsgQSend(s_evt_q, sizeof(led_event_msg_t), (UINT8 *)&msg,
                                OS_NO_SUSPEND) != OS_SUCCESS)
                {
                    XY_LOGE(led_tag, "Failed to send WiFi status event");
                }

                last_wifi = wifi;
            }
        }

        if (charger != last_charger || wakeup_charger == true)
        {
            XY_LOGI(led_tag, "Sending charger status event: %d -> %d", last_charger, charger);
            msg.event_type = LED_EVENT_CHARGER_STATUS_CHANGED;
            msg.data.charger_status = charger;

            if (OSAMsgQSend(s_evt_q, sizeof(led_event_msg_t), (UINT8 *)&msg,
                            OS_NO_SUSPEND) != OS_SUCCESS)
            {
                XY_LOGE(led_tag, "Failed to send charger status event");
            }

            last_charger = charger;
            wakeup_charger = false;
        }
    }

    XY_LOGI(led_tag, "Status task exit");
    s_status_task = NULL;
    OSATaskDelete(NULL);
}

/* 清理资源 */
static void cleanup_resources(void)
{
    s_exit_flag = true;

    /* 等待任务自行退出，或超时后强制删除 */
    uint32_t start = OSAGetTicks();
    while ((s_led_task || s_status_task) &&
           ((OSAGetTicks() - start) < TASK_EXIT_TIMEOUT_TICK))
    {
        OSATaskSleep(TASK_SLEEP_MIN_TICK);
    }

    if (s_led_task)
    {
        OSATaskDelete(s_led_task);
        s_led_task = NULL;
    }

    if (s_status_task)
    {
        OSATaskDelete(s_status_task);
        s_status_task = NULL;
    }

    if (s_evt_q)
    {
        OSAMsgQDelete(s_evt_q);
        s_evt_q = NULL;
    }
}

/* 初始化 */
int xy_led_init(void)
{
    int i;

    if (s_evt_q)
    {
        XY_LOGI(led_tag, "Already initialized");
        return 0;
    }

    XY_LOGI(led_tag, "LED module init");

    s_exit_flag = false;
    s_led_mode = LED_MODE_NORMAL;
    s_charging_state = false;
    s_led_active_until_tick = OSAGetTicks() + LED_INIT_ACTIVE_TIME_TICK;

    /* GPIO init */
    if (xy_led_gpio_init() != 0)
    {
        XY_LOGE(led_tag, "LED GPIO init failed");
        return -1;
    }

    /* 创建队列 */
    if (OSAMsgQCreate(&s_evt_q, LED_MSG_QUEUE_NAME, sizeof(led_event_msg_t),
                      LED_EVENT_QUEUE_SIZE, OS_FIFO) != OS_SUCCESS)
    {
        XY_LOGE(led_tag, "Create msgq fail");
        goto fail;
    }

    /* 创建任务 */
    if (OSATaskCreate(&s_led_task, s_led_stack, sizeof(s_led_stack),
                      LED_TASK_PRIORITY, LED_TASK_NAME,
                      led_task_entry, NULL) != OS_SUCCESS)
    {
        XY_LOGE(led_tag, "Create led task fail");
        goto fail;
    }

    if (OSATaskCreate(&s_status_task, s_status_stack, sizeof(s_status_stack),
                      STATUS_TASK_PRIORITY, STATUS_TASK_NAME,
                      led_status_task_entry, NULL) != OS_SUCCESS)
    {
        XY_LOGE(led_tag, "Create status task fail");
        goto fail;
    }

    XY_LOGI(led_tag, "LED module init ok");
    return 0;

fail:
    cleanup_resources();
    return -1;
}

/* 反初始化 */
int xy_led_deinit(void)
{
    if (!s_evt_q)
    {
        return 0;
    }

    cleanup_resources();
    XY_LOGI(led_tag, "LED module deinit ok");
    return 0;
}

/* 设置LED测试模式 */
int xy_led_set_test_mode(int enable)
{
    if (!s_evt_q)
    {
        return -1;
    }

    led_mode_e target_mode = enable ? LED_MODE_TEST : LED_MODE_NORMAL;

    if (s_led_mode == target_mode)
    {
        return 0;
    }

    led_event_msg_t msg;
    msg.event_type = LED_EVENT_TEST_MODE_CHANGED;
    msg.data.test_mode = target_mode;

    if (OSAMsgQSend(s_evt_q, sizeof(led_event_msg_t), (UINT8 *)&msg,
                    OS_NO_SUSPEND) != OS_SUCCESS)
    {
        return -1;
    }

    return 0;
}

/* 通知LED模块按键按下事件 */
int xy_led_notify_key_pressed(void)
{
    if (!s_evt_q)
    {
        return -1;
    }

    led_event_msg_t msg;
    msg.event_type = LED_EVENT_KEY_PRESSED;

    if (OSAMsgQSend(s_evt_q, sizeof(led_event_msg_t), (UINT8 *)&msg,
                    OS_NO_SUSPEND) != OS_SUCCESS)
    {
        XY_LOGE(led_tag, "Failed to send key pressed event");
        return -1;
    }

    return 0;
}
