<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>帮助</title>
<link href="css/stylesheet.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="js/base/utils.js" language="javascript"></script>
<script>
    function helpOnLoad(){
        var version = getValue("version");
        var name = getValue("name");

        if(version.indexOf("#")>-1)
            version = version.substring(0,version.indexOf("#"));


        document.getElementById("lVersionString").innerHTML = name + " " + version;

        if ("dongle" == getCookie("platform")) {
            document.getElementById("dWirelessHelpContent").style.display = "none";
            document.getElementById("lWirelessHelpNavigation").style.display = "none";
        }


    }
function getValue(varname)
{
  // First, we load the URL into a variable
  var url = window.location.href;

  // Next, split the url by the ?
  var qparts = url.split("?");

  // Check that there is a querystring, return "" if not
  if (qparts.length == 0)
  {
    return "";
  }

  // Then find the querystring, everything after the ?
  var query = qparts[1];

  // Split the query string into variables (separates by &s)
  var vars = query.split("&");

  // Initialize the value with "" as default
  var value = "";

  // Iterate through vars, checking each one for varname
  for (i=0;i<vars.length;i++)
  {
    // Split the variable by =, which splits name and value
    var parts = vars[i].split("=");

    // Check if the correct variable
    if (parts[0] == varname)
    {
      // Load value into variable
      value = parts[1];

      // End the loop
      break;
    }
  }

  // Convert escape code
  value = unescape(value);

  // Convert "+"s to " "s
  value.replace(/\+/g," ");

  // Return the value
  return value;
}
</script>
</head>

    <body class="helpBody" onload="helpOnLoad()"> <a name="top"></a>


        <div class="header" id="header">
    <div class="logo"><img src="logo.png" alt="Logo" title="Logo" /></div>
    <br class="clear" />
    <div class="navigation" id="navigation">
        <ul><li class="helpTitle">Marvell无线路由器帮助</li></ul><div class="date">&nbsp;</div>
    </div>
    </div>



	<div class="mainColumn">
    <div class="content">
 				 <ul class="helpList">
       	<li><a href="#QuickSetup">快速设置</a></li>
        <li>因特网
									<ul>
                  <li><a href="#InternetConnection">因特网连接</a></li>
				          <li><a href="#TrafficStatistics">流量统计</a></li>
				          <li><a href="#PinPuk">PIN管理</a></li>
				          <li><a href="#ManualNetwork">手动选网</a></li>
                  </ul>
        </li>
                  <li>家庭网络
           	      <ul>
                       	<li><a href="#DHCPSettings">DHCP设置</a></li>
                    <li><a href="#ConnectedDevices">连接设备</a></li>
                    <li><a href="#NetworkActivity">连接日志</a></li>
                    <li><a href="#CustomFirewallRules">用户防火墙规则</a></li>
                    <li><a href="#DomainNameFilter">域名过滤</a></li>
                    <li><a href="#Message">信息</a></li>
                    </ul>
                    </li>
                  <li id="lWirelessHelpNavigation" style="display:block">无线
           	      <ul>
                       	<li><a href="#WirelessSettings">无线网络设置</a></li>
                    <li><a href="#WirelessSecuritySettings">无线安全设置</a></li>

                    <li><a href="#WirelessMACFilters">无线MAC过滤</a></li>
                    </ul>
                    </li>
                  <li>路由器
           	      <ul>
                       	<li><a href="#UserManagement">用户管理</a></li>
                            <li><a href="#ConfigurationManagement">配置管理</a></li>
                            <li><a href="#SoftwareUpgrade">软件升级</a></li>
                            <li><a href="#RebootRouter">重启路由器</a></li>
                            <li><a href="#TimeSetting">时间设置</a></li>
                        </ul>
                    </li>
      </ul>
                 <br class="clear" />
   <a name="QuickSetup"></a>
 <h2>快速设置</h2>


<p> 路由器快速设定是用来帮助用户快速从路由器连接到因特网。快速接入过程的导向介绍了每一步如何接入因特网并且能运行的具体操作步骤。</p>
<strong>窗口1 - 用户管理</strong>
<p>     通过这个窗口用户可以修改路由器的用户名和密码。<br />
    默认的用户名是"admin", 默认的密码是"admin".<br />
</p>
<strong>窗口2 -  英特网连接设置</strong>
<p>因特网连接窗口给用户提供配置英特网连接。</p>
<p>主要的英特网连接类型包括以下几种：</p>
<ul>
  <li>关闭 - <em>没有因特网连接。只有无线家庭网络。</em></li>
  <li>手机 - 这是默认的连接模式。 用户将SIM卡插入路由器，然后即可通过关联的无线客户端接入因特网，同时在此模式下，可以支持很多的ISP，用户可以在下拉列表中选择任意一个，并且可以添加本地ISP更新路由器的ISP数据库。</li>
</ul>
<strong>窗口3 - 无线设置 - 无线网络设置</strong>
<p>无线网络设置包含无线网络的配置信息，用户可以配置无线设置和安全参数。</p>
<ul>
  <li>无线网络名（SSID）-- 用户可以定义无线网络名。</li>
  <li>无线网络密码 -- 用和可以定义无线网络的密码。</li>
  <li>验证密码 -- 确保修改的密码是准确无误的。</li>
  <li>WPA/WPA2(最安全) -- 提供最大安全等级。支持WPA或者WPA2的无线设备才能接入网络。必须输入8到63字符。</li>
  <li>WEP (较为安全) -- 该安全设置可以兼容一些老的设备。必须输入5到13字符。</li>
  <li>无加密(不推荐) -- 任何无线设备可以链接到网络，无需输入密码。</li>
</ul>

<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>

   <a name="InternetConnection"></a>
 <h2>因特网连接</h2>
<p>英特网的窗口是用来配置MARVELL路由器连接类型。有两种类型可以选择：关闭和手机。接入因特网需要和本地的ISP服务提供商获取接入信息</p>
<strong>因特网的连接设置</strong>
<p> 因特网的设置包含以下几项：</p>
我的因特网连接 -
<ul>
  <li>关闭 - <em> 没有因特网连接，只有家庭网络激活。</em></li>
  <li>手机 - 这是默认的连接模式。 用户将SIM卡插入路由器，然后即可通过关联的无线客户端接入因特网，同时在此模式下，可以支持很多的ISP，用户可以在下拉列表中选择任意一个，并且可以添加本地ISP更新路由器的ISP数据库。</li>
</ul>
网络模式 -
<ul>
<li>4G/3G/2G 多模模式 - 开启所有的网络模式 </li>
<li>4G only - 只开启4G网络</li>
<li>4G/3G only -  只开启4G和3G网络</li>
<li>3G/2G only -  只开启3G和2G网络</li>
<li>关闭 - 关闭所有的网络模式</li>
</ul>
优选网络设置 -
<ul>
<li> 4G 优先  -- 设备优先注册到4G 网络。</li>
<li> 3G/2G 优先  - 设别优先注册到3G/2G 网络。</li>
</ul>
选择ISP -
<ul>
<li> 网络接入配置文件显示配置项的名称，APN 的名称以及连接模式等信息，用户可以修改，添加和删除配置文件。</li>
</ul>
<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>

<a name="TrafficStatistics"></a>
<h2>通信量统计</h2>
 <p><strong>流量统计窗口</strong></p>

<p>流量统计窗口是统计从MARVELL路由器发送和接收到的数据包的统计。</p>
<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>

<a name="PinPuk"></a>
<h2>PIN码管理</h2>
 <p><strong>PIN码管理窗口</strong></p>

<p>开启，关闭和修改PIN码。</p>
<p>  PIN码管理窗口包含以下几项：</p>
<ul>
<li>PIN/PUK尝试-显示PIN/PUK最大尝试次数。默认的PIN码最大尝试次数是3， PUK最大尝试次数是10。如果PIN 尝试次数大于3， 将要球输入PUK 码， 如果PUK 码尝试册书大于10， SIM 就被损坏。
每输入一次错误的PIN码，尝试次数将自动减少一次，PUK 码同理。
</li>
<li>
开启/关闭PIN码-- 开启PIN 码， 输入PIN码后，按下右框“开启PIN码”，PIN码就成功开启了， 右框显示关闭PIN码。
								关闭PIN码： 输入PIN码，然后按下右框关闭PIN码， PIN 码成功关闭，又框显示开启密码。
</li>
<li>
修改PIN码   - 输入正确的PIN码， 然后输入新的PIN 码，按下保存按钮， PIN码就修改成功。
</li>
</ul>
<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>

<a name="ManualNetwork"></a>
<h2>手动选网</h2>
 <p><strong>手动选网窗口</strong></p>

<p> 手动选网窗口显示手动选网络运营商的功能，配置可选网络运营商。</p>
<p> 手动选网窗口包含以下几项：</p>
<ul>
<li>
手动选网按钮 -- 点击按钮发起路由器搜索网络运营商，扫描的结果将显示在可选网络的下拉列表中。
</li>
<li>
可选网络 -- 点击手动选择网络按钮，网络运营商列表将显示出来，选择一个网络运营商并且保存，路由器将连接到所选网络上。
</li>
<li>
系统启动时的手动选择网络 -- 选择选项“我想系统启动时手动选网”，开机后系统不会自动选择网络去注册，而是搜素出可选的网络，由用户去选择。
</li>
<li>
LTE背景扫描时间 -- 下拉列表中， 用户可以选择间隔时间并且保存，然后LTE背景扫描时间将设置到路由器中。
</li>
</ul>
<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>

<a name="DHCPSettings"></a>
<h2>DHCP设置</h2>

<p>家庭网络的窗口显示DHCP设置和允许用户的定义IP 地址。DHCP 可以动态分配IP 地址，DHCP可以确保每次动态分配给网络设备的IP 地址是不相同的。 </p>

<strong>DHCP设置</strong>

<p> DHCP设置包含以下几项：</p>
<ul>
  <li> DHCP 范围 -- 选择一个IP 地址范围， 分配起始到结尾的IP 地址范围。.</li>
  <li> 起始DHCP地址和结尾地址 -- 定义一个地址范围， 当分配一个地址给本地区域网络的计算机和设备时， 供DHCP服务器使用。</li>
</ul>

<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>


<a name="ConnectedDevices"></a>
<h2>连接的设备</h2>

<p>链接设备的窗口显示连接到路由器上的设备列表。</p>
<p>链接设备窗口包含以下几项内容：</p>
<ul>
  <li>连接设备</li>
  <li>名称 - 设备名称</li>
  <li>IP地址 - 设备的IP地址</li>
  <li>MAC地址 -设备的MAC地址</li>
  <li>连接 - 设备连接类型</li>
</ul>
<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>

<a name="NetworkActivity"></a>
<h2>连接日志</h2>
 <p>窗口显示拨号日志的列表，无线客户端链接和断开路由器的日志列表
</p>
<p>连接日志包含以下的内容:</p>
<ul>
<li>
拨号日志：列表显示了最近30个拨号记录， 包括连接因特网的时间，断开因特网的时间和因特网分配到的IP地址。
</li>
<li>
无线客户端接入日志： 最近30 无线客户端连接和断开路由器的记录，包括连接时间，断开时间和无线客户端的MAC地址。
</li>
</ul>
<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>

<a name="CustomFirewallRules"></a>
<h2>用户防火墙设置</h2>
<p>用户防火墙设置用于根据源IP地址，源端口，目的IP地址，目的端口号之间的任意配置组合来过滤设备网络访问。</p>
<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>

<a name="DomainNameFilter"></a>
<h2>域名过滤</h2>
<p>域名过滤设置用于根据域名，起始IP地址，终止IP地址之间的任意配置组合来过滤设备网络访问。 <br />可以选择使能或者关闭域名过滤功能。 <br />可以从“允许”或“拒绝”列表中有选择地增加或删除多个域名过滤器。</p>
<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>

 <a name="Message"></a>
<h2>信息</h2>
 <p> 信息窗口显示接收到的短信列表。</p>

 <div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>

<div id="dWirelessHelpContent" style="display:block">
<a name="WirelessSettings"></a>
<h2>无线网络设置</h2>

<p> 无线网络设置包含无线网络的配置信息，用户可以配置无线设置和安全参数。 </p>
<strong>设置</strong>
<p> 该设置也包含以下内容：</p>
<ul>
  <li><strong>802.11 模式</strong> --   目前提供802.11b/g模式和802.11b/g/n</li>
  <li> 802.11b/g -- 允许所有的802.11b或者802.11g设备链接到路由器上。</li>
  <li><strong>信道</strong> -- 选择路由器用的802.11频点，在家庭无线路由器无线区域内， 允许家庭路由器选择最佳的频点，802.11关联的信道范围依赖于家庭路由器是否工作在2.4 or 5 GHz范围内。信道下拉类表反映了当前选定801.11模式下的有效的信道数。
</li>
  <li><strong>带宽</strong> -- 让用户控制性能和干扰。当前此项功能不能配置，只能直接设置成20 MHz。
 </li>
 <li><strong>最大连接数</strong> -- 让用户可以配置同时连接上路由器的最大客户端数.<li>
  <li><strong>WiFi自动关闭间隔时间</strong> -- 让用户可以配置WiFi在无客户端连接时自动关闭的时间间隔.</li>
   <li><strong>主信道和次信道</strong> -- WIFI使用40Mhz带宽, 存在主信道和次信道两种信道</li>
  <li><strong>Band40 ACS 切换</strong> -- 当LTE使用band40, WiFi使用信道6,7,8,9,10, 这将避免与 LTE band40 冲突.</li>
  <li><strong>信标期设置</strong> -- 设置 WIFI 信标期.</li>
  <li><strong>DTIM 间隔设置</strong> --  设置 DTIM 间隔.</li>
</ul>

<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>
<a name="WirelessSecuritySettings"></a>
<h2>无线安全设置</h2>

<p>无线安全允许用户设置无线家庭网络安全参数。工厂默认的安全设置是WPA-WPA2 Mixed模式， 只能让WPA/WPA2设备关联到家庭路由器上。</p>
<p>无线安全设置包含以下内容:</p>
<ul>
  <li><b>无线热点的名称</b> -- 可以修改无线热点的名称</li>
  <li><b> 无线安全 </b></li>
  <li>Disabled -- 没有加密。 所有的通信信息可能被轻易获取。</li>
  <li>WPA2-PSK -- 最高等级的安全。 密码至少是8个字符，字符不应包括特殊字符，比如, #, @, % &amp; 等等。 只有支持WPA2-PSK的设备可以接入。</li>
  <li>WPA-PSK -- 较为安全的加密方式。 密码至少包含8个字符，字符不应包括特殊字符，比如, #, @, % &amp; 等等。 只有支持WPA-PSK的设备可以接入。</li>
  <li>WPA-WPA2 Mixed -- WPA2-PSK 或者 WPA-PSK 加密方式取决于需要连接到路由器的设备。 字符不应包括特殊字符，比如, #, @, % &amp; 等等. 只有支持WPA-PSK或者WPA2-PSK的设备都可以接入。</li>
  <li>WEP -- WEP加密用于不支持WPA or WPA2的设备。 WEP 加密的密码长度只能选择几组固定值。老的设备只支持5位字符(40位)，新的设备支持13个字符(104位)。13位密码的安全性能要由于5位。</li>
  <li>Password -- 用户修改密码。密码至少8位并且只能包含数字和字符，不能包含特殊字符，比如, #, @, % &amp; 等等。</li>
  <li>Verify Password --  确保修改的密码是准确无误的。</li>
  <li>WPA Cipher -- 用于设置数据通信的加密算法。临时密钥完整性协议(TKIP)提供基于WEP的每个数据包的密钥。 高级加密标准 (AES) 是另外一个非常安全的加密选项。在 &quot;TKIP 和 AES&quot; 都有时, 路由器会和客户端协商加密类型, 并当AES可用时选择AES.</li>
  <li>Encryption (when using WEP mode) -- Defines the password characters number.</li>
</ul>

<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>
<a name="WirelessMACFilters"></a>
<h2>MAC过滤</h2>
<p>无线MAC过滤可以选择使能或者关闭MAC过滤功能。 <br />可以增加和删除MAC列表</p>
<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>
</div>
<a name="UserManagement"></a>
<h2>用户管理</h2>

<p>用户管理窗口用于设置方位Web-UI的用户名和密码&quot; </p>
<strong>用户管理</strong>
<p> 用户管理窗口包含以下内容:</p>
<ul>
  <li> 路由器的用户名 -- 定义访问Web-UI的用户名。</li>
  <li> 路由器的密码 -- 定义访问Web-UI的密码。</li>
  <li> 验证密码 -- 确保修改的密码是准备无误的。</li>
</ul>
<p>缺省的登陆用户名和密码为: </p>
<ul>
  <li>路由器的用户名 -- root</li>
  <li>路由器的密码 -- admin</li>
</ul>
<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>

<a name="ConfigurationManagement"></a>
<h2>配置管理</h2>
 <p>
<strong>配置管理</strong></p>
<p>恢复出厂设置 - 这个功能会将路由器恢复出厂设置。请谨记以前的设置都会丢失。</p>

<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>

<a name="SoftwareUpgrade"></a>
<h2>软件升级</h2>

<p>软件升级界面用于升级路由器的固件。</p>
<strong>软件信息</strong>
<p>软件信息包含目前的软件版本信息。</p>
<strong>软件升级</strong>
<p>软件设计窗口被用于上传用户本地的固件升级文件。</p>
<p>软件设计包含以下内容:</p>
<ul>
  <li>选择固件升级文件 -- 在本地选择想要升级的固件升级文件。</li>
</ul>
<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>

<a name="RebootRouter"></a>
<h2>重启路由器</h2>
<p>重启路由器窗口用于重启路由器。</p>

<a name="TimeSetting"></a>
<h2>时间设置</h2>

 <p>时间设置窗口用于配置路由器的时间。</p>
<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>

<a name="WebDavManagement"></a>
<h2>SD卡管理</h2>

<p>管理SD卡使用方式和网络使用SD卡时的用户名和密码。 </p>
<strong>SD卡使用管理</strong>
<p> SD卡使用管理窗口包含以下内容:</p>
<ul>
  <li> SD卡使用模式设置 -- 设置SD卡访问是通过网络还是USB连接。</li>
  <li> SD卡用户管理 -- 设置网络使用SD卡时的用户名和密码。</li>
</ul>
<p>缺省的登陆用户名和密码为: </p>
<ul>
  <li>用户名 -- admin</li>
  <li>密码 -- admin</li>
</ul>
<div align="right"><a href="#top"><strong>&uarr; </strong> Top</a></div>
    </div>
    <br class="clear" />

</div>
        <div class="footerHelp">
        <div class="footer">
<div class="footerLft"></div>
    <div class="footerMast">
        <div>Copyright 2017, All Rights Reserved.</div>
        <span class="logoTxt"></span>
        <span class="footerTxt"><label id="lVersionString"> </label></span>
    </div>
    <div class="footerRgt"></div>
        </div> <br class="clear" /></div>

</body>
</html>
