<div class="header" id="header">
    <div class="logo"><img src="logo.png" alt="Logo" title="Logo" /></div>
    <div class="loginArea"><label id="lableWelcome">Welcome</label> <a href="#"></a> <br />
        <span id="quickSetupSpan"><a href="#." onclick="quickSetup()" id="quickSetup" >Quick Setup</a>  |  <a href="#." id="MainHelp" onclick="getMainHelp()">Help</a>  |  <a href="#." id="MainLogOut" onclick="logOut()">Log Out</a></span>
    </div>
    <br class="clear" />
    <div class="navigation" id="navigation">
        <ul id ="menu" ></ul><!--<div class="date" id="divDateTime"></div>-->
    </div>
</div>
<div class="mainColumn" id="mainColumn">
    <div class="leftBar">
        <ul class="leftMenu" id="submenu"> </ul>
    </div>

    <div id="Content" class="content">

    </div>
    <br class="clear" /><br class="clear" />
</div>
<div id="alertMB" style="display: none">

    <h1 id="lAlert"></h1>

    <a href="#" class="close" onclick="hm()"><img src="images/close-icon.gif" alt="" /></a><br style="clear:both" />

 <div align="center">
     <label id="lAlertMessage" class="lable12"></label>
    <br style="clear:both" />
    <div class="buttonRow1" style="border-top:1px solid #a7a7a7; padding:7px 0; margin:7px 0 0 0; text-align:center">
        <span class="btnWrp"><input id="btnModalOk" type="button"  value="OK" onclick="hm()" /></span>
</div>
    </div>

</div>
<div id="mbox"  style="display: none">
    <div class="" id="PleaseWait" >

        <div align="center">
            <br />
            <img src="images/PleaseWait.png" alt="..." class="loading" id="WaitPic" />
            <br />
            <label id="lPleaseWait"></label>
        </div>
    </div>
</div>
<script type="text/javascript">
           function  displayControls(){
               document.getElementById("laUsername").innerHTML=jQuery.i18n.prop("laUsername");
                document.getElementById("laPassword").innerHTML=jQuery.i18n.prop("laPassword");
                document.getElementById("btnSignIn").value = jQuery.i18n.prop("btnSignIn");
	       document.getElementById("lSignIn").innerHTML = jQuery.i18n.prop("lTitle");
	       document.getElementById("lacopyright").innerHTML = jQuery.i18n.prop("lacopyright");

           }
</script>

<div id="mbox1"  style="display: none">
    <div class="popUpBox popUpBox3" id="MBQuickSetup">
        <div class="pBoxCont" style="padding:10px 55px 10px 20px;">
            <h2 class="subtlt" id="QsText"></h2>

            <p id="QsText1"></p>
            <p id="QsText2"></p>
			<br />
			<br />
			<div align='center'>
			<span class="btnWrp">
			<input   type="button" id="btnQuickSetup" value="Quick Setup" onclick="hm(); quickSetup()"  style="padding:3px 3px;"/>
			</span>
			</div>
			<br />
			<br / >
			<br / >
            <input id="chkSkip" type="checkbox" value="" class="chk" /><label id="QsText3" style="width:230px; color:#333; padding:2px 0 0 0;">Don't show Quick Setup in future.</label>
            <span class="btnWrp skip" style="margin-left: auto;margin-right: auto;">   <input id="btnSkip" type="button" value="Skip"  onclick="btnSkipQSClicked()" /></span>


            <br style="clear:both" />
            <label class="error" style="width:500px; margin-left:22px" id="QsText4"></label>
            <br style="clear:both" />

        </div>
    </div> </div>
<div class="footer">
    <div class="footerLft"></div>
    <div class="footerMast">
        <div id="lacopyright">Copyright 2017, All Rights Reserved, ASR Ltd.</div>
        <span class="logoTxt">A S R</span>
        <span class="footerTxt"><label id="lVesrion"> </label></span>
    </div>
    <div class="footerRgt"></div>
</div>
